# Makefile for TX ISP Driver
# This uses the Linux kernel build system (Kbuild)

# Default kernel source directory - adjust as needed
KERNEL_SRC ?= /lib/modules/$(shell uname -r)/build

# Current directory
PWD := $(shell pwd)

# Default target
all: modules

# Build kernel modules using Kbuild
modules:
	$(MAKE) -C $(KERNEL_SRC) M=$(PWD) modules

# Clean build artifacts
clean:
	$(MAKE) -C $(KERNEL_SRC) M=$(PWD) clean
	rm -f *.o *.ko *.mod.c *.mod.o *.symvers *.order

# Install modules (requires root)
install: modules
	$(MAKE) -C $(KERNEL_SRC) M=$(PWD) modules_install

# Help target
help:
	@echo "Available targets:"
	@echo "  all/modules - Build kernel modules"
	@echo "  clean       - Clean build artifacts"
	@echo "  install     - Install modules (requires root)"
	@echo "  help        - Show this help"

.PHONY: all modules clean install help
